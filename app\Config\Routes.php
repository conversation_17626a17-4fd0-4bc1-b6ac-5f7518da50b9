<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');

// Admin Routes
$routes->group('admin', ['namespace' => 'App\Controllers'], function($routes) {
    // Dashboard
    $routes->get('/', 'Admin::index');
    $routes->get('dashboard', 'Admin::dashboard');

    // Students Management
    $routes->group('students', function($routes) {
        $routes->get('/', 'StudentsController::index');
        $routes->get('create', 'StudentsController::create');
        $routes->post('store', 'StudentsController::store');
        $routes->get('edit/(:num)', 'StudentsController::edit/$1');
        $routes->post('update/(:num)', 'StudentsController::update/$1');
        $routes->delete('delete/(:num)', 'StudentsController::delete/$1');
        $routes->get('show/(:num)', 'StudentsController::show/$1');
        $routes->post('toggle-status/(:num)', 'StudentsController::toggleStatus/$1');
        $routes->post('bulk-delete', 'StudentsController::bulkDelete');
        $routes->get('export/(:alpha)', 'StudentsController::export/$1');
        $routes->post('get-data', 'StudentsController::getData');
        $routes->post('get-by-class-section', 'StudentsController::getByClassSection');
        $routes->post('search', 'StudentsController::search');
        $routes->get('statistics', 'StudentsController::statistics');
        $routes->get('promote', 'StudentsController::promote');
        $routes->post('process-promotion', 'StudentsController::processPromotion');
        $routes->get('import', 'StudentsController::import');
        $routes->post('process-import', 'StudentsController::processImport');
    });

    // Staff Management
    $routes->group('staff', function($routes) {
        $routes->get('/', 'StaffController::index');
        $routes->get('create', 'StaffController::create');
        $routes->post('store', 'StaffController::store');
        $routes->get('edit/(:num)', 'StaffController::edit/$1');
        $routes->post('update/(:num)', 'StaffController::update/$1');
        $routes->delete('delete/(:num)', 'StaffController::delete/$1');
        $routes->get('show/(:num)', 'StaffController::show/$1');
        $routes->post('toggle-status/(:num)', 'StaffController::toggleStatus/$1');
        $routes->post('bulk-delete', 'StaffController::bulkDelete');
        $routes->get('export/(:alpha)', 'StaffController::export/$1');
        $routes->post('get-data', 'StaffController::getData');
        $routes->post('get-by-department', 'StaffController::getByDepartment');
        $routes->get('statistics', 'StaffController::statistics');
        $routes->get('attendance', 'StaffController::attendance');
        $routes->post('mark-attendance', 'StaffController::markAttendance');
        $routes->get('attendance-report', 'StaffController::attendanceReport');
        $routes->get('import', 'StaffController::import');
        $routes->post('process-import', 'StaffController::processImport');
    });

    // Classes Management
    $routes->group('classes', function($routes) {
        $routes->get('/', 'ClassesController::index');
        $routes->get('create', 'ClassesController::create');
        $routes->post('store', 'ClassesController::store');
        $routes->get('edit/(:num)', 'ClassesController::edit/$1');
        $routes->post('update/(:num)', 'ClassesController::update/$1');
        $routes->delete('delete/(:num)', 'ClassesController::delete/$1');
        $routes->get('show/(:num)', 'ClassesController::show/$1');
        $routes->post('toggle-status/(:num)', 'ClassesController::toggleStatus/$1');
        $routes->post('bulk-delete', 'ClassesController::bulkDelete');
        $routes->get('export/(:alpha)', 'ClassesController::export/$1');
        $routes->post('get-data', 'ClassesController::getData');
        $routes->get('get-sections/(:num)', 'ClassesController::getSections/$1');
        $routes->post('assign-sections/(:num)', 'ClassesController::assignSections/$1');
        $routes->get('statistics', 'ClassesController::statistics');
        $routes->get('sections/(:num)', 'ClassesController::sections/$1');
    });

    // Expenses Management
    $routes->group('expenses', function($routes) {
        $routes->get('/', 'ExpensesController::index');
        $routes->get('create', 'ExpensesController::create');
        $routes->post('store', 'ExpensesController::store');
        $routes->get('edit/(:num)', 'ExpensesController::edit/$1');
        $routes->post('update/(:num)', 'ExpensesController::update/$1');
        $routes->delete('delete/(:num)', 'ExpensesController::delete/$1');
        $routes->get('show/(:num)', 'ExpensesController::show/$1');
        $routes->post('toggle-status/(:num)', 'ExpensesController::toggleStatus/$1');
        $routes->post('bulk-delete', 'ExpensesController::bulkDelete');
        $routes->get('export/(:alpha)', 'ExpensesController::export/$1');
        $routes->post('get-data', 'ExpensesController::getData');
        $routes->post('get-filtered', 'ExpensesController::getFiltered');
        $routes->get('statistics', 'ExpensesController::statistics');
        $routes->post('monthly-chart', 'ExpensesController::monthlyChart');
        $routes->post('category-chart', 'ExpensesController::categoryChart');
        $routes->post('soft-delete/(:num)', 'ExpensesController::softDelete/$1');
        $routes->post('restore/(:num)', 'ExpensesController::restore/$1');
        $routes->get('trash', 'ExpensesController::trash');
        $routes->get('reports', 'ExpensesController::reports');
        $routes->post('generate-report', 'ExpensesController::generateReport');
    });

    // Legacy routes for backward compatibility
    $routes->get('fees', 'Admin::fees');
    $routes->get('reports', 'Admin::reports');
    $routes->get('settings', 'Admin::settings');
});
