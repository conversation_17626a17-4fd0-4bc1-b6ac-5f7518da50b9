<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="sm:flex sm:items-center mb-6">
    <div class="sm:flex-auto">
        <h1 class="text-2xl font-semibold text-gray-900">Classes Management</h1>
        <p class="mt-2 text-sm text-gray-700">Manage classes, sections, and student assignments.</p>
    </div>
    <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
        <button type="button" onclick="addClass()" class="inline-flex items-center justify-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 sm:w-auto">
            <i class="fas fa-plus mr-2"></i>
            Add Class
        </button>
    </div>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 gap-5 sm:grid-cols-3 mb-6">
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-school text-blue-600"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Classes</dt>
                        <dd class="text-lg font-bold text-gray-900"><?= count($classes ?? []) ?></dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-green-600"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Students</dt>
                        <dd class="text-lg font-bold text-gray-900">
                            <?= array_sum(array_column($classes ?? [], 'student_count')) ?>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-purple-600"></i>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Avg Students/Class</dt>
                        <dd class="text-lg font-bold text-gray-900">
                            <?= count($classes ?? []) > 0 ? round(array_sum(array_column($classes ?? [], 'student_count')) / count($classes)) : 0 ?>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Classes Grid -->
<div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 mb-8">
    <?php if (!empty($classes)): ?>
        <?php foreach ($classes as $class): ?>
            <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200 hover:shadow-md transition-shadow duration-200">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-graduation-cap text-primary-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-medium text-gray-900"><?= esc($class['class']) ?></h3>
                                <p class="text-sm text-gray-500">Class <?= esc($class['class']) ?></p>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="editClass(<?= $class['id'] ?>)" class="text-yellow-600 hover:text-yellow-900" title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="deleteClass(<?= $class['id'] ?>)" class="text-red-600 hover:text-red-900" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500">Students Enrolled:</span>
                            <span class="text-sm font-medium text-gray-900"><?= $class['student_count'] ?? 0 ?></span>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500">Status:</span>
                            <?php if (($class['is_active'] ?? 'no') === 'yes'): ?>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    Active
                                </span>
                            <?php else: ?>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                    Inactive
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <button onclick="viewClassDetails(<?= $class['id'] ?>)" class="w-full bg-primary-50 text-primary-700 hover:bg-primary-100 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200">
                            View Details
                        </button>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php else: ?>
        <div class="col-span-full">
            <div class="text-center py-12">
                <i class="fas fa-school text-gray-400 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Classes Found</h3>
                <p class="text-gray-500 mb-6">Get started by creating your first class.</p>
                <button onclick="addClass()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
                    <i class="fas fa-plus mr-2"></i>
                    Add First Class
                </button>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Classes Table -->
<div class="bg-white shadow-sm rounded-lg border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">All Classes</h3>
        <p class="text-sm text-gray-500">Detailed view of all classes with student counts</p>
    </div>
    <div class="p-6">
        <div class="overflow-x-auto">
            <table id="classesTable" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class Name</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Students</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php if (!empty($classes)): ?>
                        <?php foreach ($classes as $class): ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 bg-primary-100 rounded-lg flex items-center justify-center">
                                                <i class="fas fa-graduation-cap text-primary-600"></i>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                Class <?= esc($class['class']) ?>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                ID: <?= $class['id'] ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?= $class['student_count'] ?? 0 ?> students</div>
                                    <div class="text-sm text-gray-500">enrolled</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if (($class['is_active'] ?? 'no') === 'yes'): ?>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                            Active
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                            Inactive
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?= date('M j, Y', strtotime($class['created_at'])) ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="viewClassDetails(<?= $class['id'] ?>)" class="text-primary-600 hover:text-primary-900" title="View">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button onclick="editClass(<?= $class['id'] ?>)" class="text-yellow-600 hover:text-yellow-900" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button onclick="deleteClass(<?= $class['id'] ?>)" class="text-red-600 hover:text-red-900" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Initialize DataTable
    $(document).ready(function() {
        $('#classesTable').DataTable({
            responsive: true,
            order: [[0, 'asc']],
            columnDefs: [
                { orderable: false, targets: [4] } // Disable sorting on Actions column
            ]
        });
    });

    // Add Class Function
    function addClass() {
        Swal.fire({
            title: 'Add New Class',
            html: `
                <div class="text-left">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Class Name</label>
                        <input type="text" id="className" placeholder="e.g., 1st Grade, 2nd Grade" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select id="classStatus" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            <option value="yes">Active</option>
                            <option value="no">Inactive</option>
                        </select>
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Add Class',
            confirmButtonColor: '#3b82f6',
            cancelButtonText: 'Cancel',
            preConfirm: () => {
                const className = document.getElementById('className').value;
                const status = document.getElementById('classStatus').value;

                if (!className) {
                    Swal.showValidationMessage('Class name is required');
                    return false;
                }

                return { className, status };
            }
        }).then((result) => {
            if (result.isConfirmed) {
                showToast('Class added successfully!', 'success');
                setTimeout(() => location.reload(), 1500);
            }
        });
    }

    // View Class Details Function
    function viewClassDetails(id) {
        Swal.fire({
            title: 'Class Details',
            html: `
                <div class="text-left">
                    <p><strong>Class ID:</strong> ${id}</p>
                    <p><strong>Students:</strong> Loading...</p>
                    <p><strong>Sections:</strong> Loading...</p>
                    <p><strong>Teachers:</strong> Loading...</p>
                    <p class="text-sm text-gray-500 mt-4">Full class details would be loaded here via AJAX.</p>
                </div>
            `,
            confirmButtonText: 'Close',
            confirmButtonColor: '#3b82f6'
        });
    }

    // Edit Class Function
    function editClass(id) {
        Swal.fire({
            title: 'Edit Class',
            html: `
                <div class="text-left">
                    <p class="text-sm text-gray-500 mb-4">Class ID: ${id}</p>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Class Name</label>
                        <input type="text" id="editClassName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select id="editClassStatus" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500">
                            <option value="yes">Active</option>
                            <option value="no">Inactive</option>
                        </select>
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Update Class',
            confirmButtonColor: '#3b82f6',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                showToast('Class updated successfully!', 'success');
                setTimeout(() => location.reload(), 1500);
            }
        });
    }

    // Delete Class Function
    function deleteClass(id) {
        Swal.fire({
            title: 'Are you sure?',
            text: "This will also affect all students enrolled in this class!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#ef4444',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                showToast('Class deleted successfully!', 'success');
                setTimeout(() => location.reload(), 1500);
            }
        });
    }
</script>
<?= $this->endSection() ?>
