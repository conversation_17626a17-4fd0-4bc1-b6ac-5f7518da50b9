<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');

// Admin Routes
$routes->group('admin', ['namespace' => 'App\Controllers'], function($routes) {
    $routes->get('/', 'Admin::index');
    $routes->get('dashboard', 'Admin::dashboard');
    $routes->get('students', 'Admin::students');
    $routes->get('staff', 'Admin::staff');
    $routes->get('classes', 'Admin::classes');
    $routes->get('fees', 'Admin::fees');
    $routes->get('expenses', 'Admin::expenses');
    $routes->get('reports', 'Admin::reports');
    $routes->get('settings', 'Admin::settings');
});
